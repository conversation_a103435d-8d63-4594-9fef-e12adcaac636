// View Transitions API styles
// Adapté de transition_source/main.css pour le système SCSS existant

// Configuration des transitions de base
::view-transition-old(root),
::view-transition-new(root) {
  animation-duration: 0.5s;
}

// Animation de sortie - l'ancienne page
@keyframes move-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0.4;
    transform: translateY(-35%);
  }
}

// Animation d'entrée - la nouvelle page
@keyframes move-in {
  from {
    clip-path: polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%);
  }
  to {
    clip-path: polygon(0% 100%, 100% 100%, 100% 0%, 0% 0%);
  }
}

// Application des animations aux transitions
::view-transition-old(root) {
  animation: 1.5s cubic-bezier(0.87, 0, 0.13, 1) both move-out;
}

::view-transition-new(root) {
  animation: 1.5s cubic-bezier(0.87, 0, 0.13, 1) both move-in;
}

// Styles pour les éléments de navigation avec clip-path
.view-transition-link {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  
  a {
    display: inline-block;
    position: relative;
    transform: translateY(16px);
    will-change: transform;
    text-decoration: none;
    transition: transform 0.3s ease;
  }
}

// Styles pour les titres avec animations de caractères
.view-transition-title {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  
  .char {
    position: relative;
    will-change: transform;
    display: inline-block;
  }
}

// Styles pour les paragraphes avec animations de lignes
.view-transition-text {
  .line {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
    
    span {
      position: relative;
      will-change: transform;
      display: block;
    }
  }
}

// Support pour les navigateurs qui ne supportent pas View Transitions
@supports not (view-transition-name: none) {
  // Fallback animations pour les navigateurs non compatibles
  .view-transition-fallback {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
    
    &.active {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// Optimisations de performance
.view-transition-container {
  contain: layout style paint;
  will-change: transform, opacity;
}

// Styles pour les états de chargement
.view-transition-loading {
  pointer-events: none;
  
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 9999;
  }
}
