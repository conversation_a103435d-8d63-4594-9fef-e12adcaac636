"use client";

import { useEffect, useRef, useContext, useCallback } from "react";
import { useRouter, usePathname } from "next/navigation";
import { LenisScrollContext } from "@/components/LocomotiveScrollProvider";
import { useViewTransitionAnimations } from "@/hooks/useViewTransitionAnimations";
import { gsap } from "gsap";

const ViewTransition = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const lenisScroll = useContext(LenisScrollContext);
  const { initializeAnimations } = useViewTransitionAnimations();
  const isTransitioning = useRef(false);
  const initTimeoutRef = useRef(null);

  // Vérifier si le navigateur supporte View Transitions
  const supportsViewTransitions = useCallback(() => {
    return typeof document !== 'undefined' && 
           'startViewTransition' in document;
  }, []);

  // Initialiser Lenis après une transition
  const initializeLenis = useCallback(() => {
    if (lenisScroll) {
      // Réinitialiser Lenis après une transition
      lenisScroll.start();
      lenisScroll.scrollTo(0, { immediate: true });
    }
  }, [lenisScroll]);

  // Note: initializeAnimations est maintenant fourni par le hook useViewTransitionAnimations

  // Gérer la navigation avec View Transitions
  const handleNavigation = useCallback((url) => {
    if (isTransitioning.current) return;
    
    isTransitioning.current = true;

    // Arrêter Lenis pendant la transition
    if (lenisScroll) {
      lenisScroll.stop();
    }

    if (supportsViewTransitions()) {
      // Utiliser l'API View Transitions
      const transition = document.startViewTransition(() => {
        router.push(url);
      });

      transition.ready.then(() => {
        // Scroll vers le haut
        window.scrollTo(0, 0);
        
        // Réinitialiser les animations après la transition
        if (initTimeoutRef.current) {
          clearTimeout(initTimeoutRef.current);
        }
        
        initTimeoutRef.current = setTimeout(() => {
          initializeAnimations();
          initializeLenis();
          isTransitioning.current = false;
        }, 100);
      });

      transition.finished.catch(() => {
        // En cas d'erreur, s'assurer que l'état est réinitialisé
        isTransitioning.current = false;
        initializeLenis();
      });
    } else {
      // Fallback pour les navigateurs non compatibles
      router.push(url);
      setTimeout(() => {
        initializeAnimations();
        initializeLenis();
        isTransitioning.current = false;
      }, 100);
    }
  }, [router, lenisScroll, supportsViewTransitions, initializeAnimations, initializeLenis]);

  // Intercepter les clics sur les liens internes
  const handleLinkClick = useCallback((e) => {
    if (isTransitioning.current) {
      e.preventDefault();
      return;
    }

    // Vérifier si c'est un lien interne
    const href = e.currentTarget.href;
    if (!href) return;

    const url = new URL(href);
    const isInternal = url.origin === window.location.origin;
    const isCurrentPage = url.pathname === pathname;

    // Ignorer les liens externes, les liens avec modificateurs, ou la page actuelle
    if (!isInternal || 
        isCurrentPage ||
        e.metaKey || 
        e.ctrlKey || 
        e.shiftKey || 
        e.altKey || 
        e.button !== 0 ||
        e.currentTarget.target === "_blank") {
      return;
    }

    e.preventDefault();
    handleNavigation(url.pathname);
  }, [pathname, handleNavigation]);

  // Initialiser les animations au chargement de la page
  useEffect(() => {
    if (initTimeoutRef.current) {
      clearTimeout(initTimeoutRef.current);
    }

    initTimeoutRef.current = setTimeout(() => {
      initializeAnimations();
      isTransitioning.current = false;
    }, 100);

    return () => {
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
      }
    };
  }, [pathname, initializeAnimations]);

  // Ajouter les event listeners pour les liens
  useEffect(() => {
    const links = document.querySelectorAll('a[href^="/"]');
    
    links.forEach((link) => {
      link.addEventListener('click', handleLinkClick);
    });

    return () => {
      links.forEach((link) => {
        link.removeEventListener('click', handleLinkClick);
      });
    };
  }, [handleLinkClick]);

  return (
    <div className="view-transition-container">
      {children}
    </div>
  );
};

export default ViewTransition;
