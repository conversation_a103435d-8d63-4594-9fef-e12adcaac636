'use client';

import { useTranslation } from '@/hooks/useTranslation';
import Link from 'next/link';
import styles from './page.module.scss';

export default function TransitionDemoClient({ params }) {
  const locale = params.locale || 'fr';
  const { t } = useTranslation('navigation');

  return (
    <main className={styles.main}>
      {/* Navigation avec classes de transition */}
      <nav className={styles.nav}>
        <div className="view-transition-link">
          <Link href={`/${locale}`}>Accueil</Link>
        </div>
        <div className="view-transition-link">
          <Link href={`/${locale}/agency`}>Agence</Link>
        </div>
        <div className="view-transition-link">
          <Link href={`/${locale}/blog`}>Blog</Link>
        </div>
        <div className="view-transition-link">
          <Link href={`/${locale}/contact`}>Contact</Link>
        </div>
      </nav>

      {/* Conteneur principal */}
      <div className={styles.container}>
        {/* Titre héro avec animation de caractères */}
        <div className={styles.hero}>
          <h1 className="view-transition-title">Transitions</h1>
        </div>

        {/* Section avec images */}
        <div className={styles.images}>
          <img src="/images/lucas-joliveau-siege-ordinateur-portable.png" alt="Demo 1" />
          <img src="/images/lucas-joliveau-siege-ordinateur-portable.png" alt="Demo 2" />
          <img src="/images/lucas-joliveau-siege-ordinateur-portable.png" alt="Demo 3" />
        </div>

        {/* Section info avec texte animé */}
        <div className={styles.info}>
          <div className={styles.col}>
            <img src="/images/lucas-joliveau-siege-ordinateur-portable.png" alt="Portrait" />
          </div>
          <div className={styles.col}>
            <div className="view-transition-text">
              <p>
                Cette page démontre les transitions modernes utilisant l'API View Transitions du navigateur. 
                Les animations sont fluides et performantes, créant une expérience utilisateur immersive. 
                Chaque élément est animé avec des délais échelonnés pour un effet visuel saisissant.
              </p>
            </div>
          </div>
        </div>

        {/* Liens de test */}
        <div className={styles.testLinks}>
          <h2>Tester les transitions :</h2>
          <div className={styles.linkGrid}>
            <div className="view-transition-link">
              <Link href={`/${locale}`}>← Retour à l'accueil</Link>
            </div>
            <div className="view-transition-link">
              <Link href={`/${locale}/agency`}>Voir l'agence →</Link>
            </div>
            <div className="view-transition-link">
              <Link href={`/${locale}/blog`}>Lire le blog →</Link>
            </div>
            <div className="view-transition-link">
              <Link href={`/${locale}/contact`}>Nous contacter →</Link>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
