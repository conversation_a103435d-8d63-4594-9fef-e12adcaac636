// app/[locale]/layout.js
import '../globals.scss';
import { Plus_Jakarta_Sans } from 'next/font/google';
import GoogleAnalytics from '../GoogleAnalytics';
import Header from '@/components/Header';
import PageContainer from '@/components/Header/PageContainer';
import SelectionColorChanger from '@/common/Selection';
import CookieConsent from '@/components/CookieConsent';
import Footer from '@/components/Footer';
import CanonicalHead from '../CanonicalHead';
import Head from 'next/head';
import LenisScrollProvider from '@/components/LocomotiveScrollProvider';
import PageTransition from '@/components/PageTransition';
import ViewTransition from '@/components/ViewTransition';
import 'material-symbols';
import { getTranslation } from '@/hooks/useTranslation';

const plusJakartaSans = Plus_Jakarta_Sans({ subsets: ['latin'] });

const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Kapreon",
  "url": "https://kapreon.com",
  "logo": "https://kapreon.com/logo.png"
};

export async function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const t = getTranslation(locale, 'home');

  return {
    metadataBase: new URL('https://kapreon.com'),
    title: t('title'),
    description: t('description'),
    verification: {
      google: '1XC-nn6NtCPE3WayReHxS2iSwBKwDk8o79nqknbwDMU',
    },
    openGraph: {
      title: t('title'),
      description: t('description'),
      images: [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com${locale === 'en' ? '/en' : ''}`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: t('title'),
      description: t('description'),
      images: ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function LocaleLayout({ children, params }) {
  const locale = params.locale || 'fr';
  
  return (
    <html lang={locale}>
      <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
        />
      </Head>
      <CanonicalHead />
      <body className={plusJakartaSans.className}>
        <GoogleAnalytics />
        <SelectionColorChanger />
        <ViewTransition>
          <PageTransition>
            <Header locale={locale} />
            <PageContainer>
              <LenisScrollProvider
                options={{
                  smooth: true,
                  smartphone: { smooth: true },
                  tablet: { smooth: true },
                }}
              >
                {children}
                <Footer locale={locale} />
              </LenisScrollProvider>
            </PageContainer>
            <CookieConsent locale={locale} />
          </PageTransition>
        </ViewTransition>
      </body>
    </html>
  );
} 

export async function generateStaticParams() {
  return [
    { locale: 'fr' },
    { locale: 'en' },
  ];
}
