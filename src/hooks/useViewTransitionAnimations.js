"use client";

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';

export const useViewTransitionAnimations = () => {
  const [SplitText, setSplitText] = useState(null);
  const animationsRef = useRef([]);

  // Charger SplitText dynamiquement
  useEffect(() => {
    if (typeof window !== 'undefined' && !SplitText) {
      // Essayer d'importer SplitText depuis GSAP
      import('gsap/SplitText')
        .then((module) => {
          const SplitTextClass = module.SplitText || module.default;
          setSplitText(() => SplitTextClass);
          gsap.registerPlugin(SplitTextClass);
        })
        .catch(() => {
          // Si SplitText n'est pas disponible, essayer split-type
          import('split-type')
            .then((module) => {
              const SplitType = module.default;
              setSplitText(() => SplitType);
              // Adapter SplitType pour avoir une API similaire à SplitText
              window.SplitText = SplitType;
            })
            .catch((error) => {
              console.warn('Aucune librairie de split text disponible:', error);
            });
        });
    }
  }, [SplitText]);

  // Nettoyer les animations précédentes
  const clearAnimations = () => {
    animationsRef.current.forEach((animation) => {
      if (animation.kill) {
        animation.kill();
      }
    });
    animationsRef.current = [];
  };

  // Animer les liens de navigation
  const animateNavLinks = (delay = 0.5) => {
    const navLinks = document.querySelectorAll('.view-transition-link a');
    if (navLinks.length > 0) {
      gsap.set(navLinks, { y: 16 });
      const tl = gsap.to(navLinks, {
        y: 0,
        duration: 1,
        stagger: 0.1,
        ease: "power4.out",
        delay,
      });
      animationsRef.current.push(tl);
      return tl;
    }
    return null;
  };

  // Animer les titres avec caractères
  const animateHeroTitles = (delay = 0.5) => {
    const heroTitles = document.querySelectorAll('.view-transition-title');
    const animations = [];

    heroTitles.forEach((title) => {
      if (SplitText) {
        try {
          const splitText = new SplitText(title, { types: "chars" });
          gsap.set(splitText.chars, { y: 400 });
          const tl = gsap.to(splitText.chars, {
            y: 0,
            duration: 1,
            stagger: 0.075,
            ease: "power4.out",
            delay,
          });
          animations.push(tl);
        } catch (error) {
          console.warn('Erreur avec SplitText:', error);
          // Fallback animation
          gsap.set(title, { y: 50, opacity: 0 });
          const tl = gsap.to(title, {
            y: 0,
            opacity: 1,
            duration: 1,
            ease: "power4.out",
            delay,
          });
          animations.push(tl);
        }
      } else {
        // Animation de fallback sans SplitText
        gsap.set(title, { y: 50, opacity: 0 });
        const tl = gsap.to(title, {
          y: 0,
          opacity: 1,
          duration: 1,
          ease: "power4.out",
          delay,
        });
        animations.push(tl);
      }
    });

    animationsRef.current.push(...animations);
    return animations;
  };

  // Animer les paragraphes avec lignes
  const animateTextLines = (delay = 0.25) => {
    const textElements = document.querySelectorAll('.view-transition-text');
    const animations = [];

    textElements.forEach((textEl) => {
      if (SplitText) {
        try {
          // Nettoyer les anciens splits
          const existingLines = textEl.querySelectorAll('.line');
          existingLines.forEach(line => {
            const text = line.textContent;
            line.parentNode.replaceChild(document.createTextNode(text), line);
          });

          const splitText = new SplitText(textEl, {
            types: "lines",
            tagName: "div",
            lineClass: "line",
          });

          // Envelopper chaque ligne dans un span
          splitText.lines.forEach((line) => {
            const content = line.innerHTML;
            line.innerHTML = `<span>${content}</span>`;
          });

          const spans = textEl.querySelectorAll('.line span');
          gsap.set(spans, { y: 400, display: "block" });
          const tl = gsap.to(spans, {
            y: 0,
            duration: 2,
            stagger: 0.075,
            ease: "power4.out",
            delay,
          });
          animations.push(tl);
        } catch (error) {
          console.warn('Erreur avec SplitText pour les lignes:', error);
          // Fallback animation
          gsap.set(textEl, { y: 30, opacity: 0 });
          const tl = gsap.to(textEl, {
            y: 0,
            opacity: 1,
            duration: 1.5,
            ease: "power4.out",
            delay,
          });
          animations.push(tl);
        }
      } else {
        // Animation de fallback sans SplitText
        gsap.set(textEl, { y: 30, opacity: 0 });
        const tl = gsap.to(textEl, {
          y: 0,
          opacity: 1,
          duration: 1.5,
          ease: "power4.out",
          delay,
        });
        animations.push(tl);
      }
    });

    animationsRef.current.push(...animations);
    return animations;
  };

  // Fonction principale pour initialiser toutes les animations
  const initializeAnimations = () => {
    clearAnimations();
    
    // Délais échelonnés pour les différents éléments
    animateNavLinks(0.5);
    animateHeroTitles(0.5);
    animateTextLines(0.25);
  };

  // Nettoyer lors du démontage
  useEffect(() => {
    return () => {
      clearAnimations();
    };
  }, []);

  return {
    SplitText,
    initializeAnimations,
    animateNavLinks,
    animateHeroTitles,
    animateTextLines,
    clearAnimations,
  };
};
